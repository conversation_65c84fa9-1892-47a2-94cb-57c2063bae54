#!/usr/bin/env python3
"""
修正版：分析帧0通道0的原始特征和重建特征的误差
使用合理的误差评估方法，而不是量化步长
"""

import numpy as np

def analyze_frame0_channel0_corrected():
    """修正版：分析帧0通道0的误差"""
    
    print("=== 帧0通道0误差分析（修正版）===")
    print("注意：原始数据和重建数据都是真实特征值，不是0-1归一化数据")
    
    try:
        # 加载通道0的数据
        original = np.load('exported_first10_non_resize_channels/channel_000_original.npy')
        reconstructed = np.load('exported_first10_non_resize_channels/channel_000_reconstructed.npy')
        difference = np.load('exported_first10_non_resize_channels/channel_000_difference.npy')
        
        print(f"✅ 数据加载成功")
        print(f"   数据形状: {original.shape}")
        print(f"   总像素数: {original.size}")
        
        # 基本统计信息
        print(f"\n📊 基本统计信息:")
        print(f"   原始数据范围: [{original.min():.6f}, {original.max():.6f}]")
        print(f"   重建数据范围: [{reconstructed.min():.6f}, {reconstructed.max():.6f}]")
        print(f"   数据动态范围: {original.max() - original.min():.6f}")
        print(f"   绝对误差范围: [{difference.min():.6f}, {difference.max():.6f}]")
        print(f"   平均绝对误差: {difference.mean():.6f}")
        print(f"   误差标准差: {difference.std():.6f}")
        
        # 相对误差分析（这个是有意义的）
        print(f"\n🔍 相对误差分析:")
        non_zero_mask = np.abs(original) > 1e-8
        if np.any(non_zero_mask):
            relative_errors = difference[non_zero_mask] / np.abs(original[non_zero_mask])
            print(f"   非零像素数: {np.sum(non_zero_mask)}")
            print(f"   相对误差范围: [{relative_errors.min()*100:.2f}%, {relative_errors.max()*100:.2f}%]")
            print(f"   平均相对误差: {relative_errors.mean()*100:.2f}%")
            print(f"   相对误差标准差: {relative_errors.std()*100:.2f}%")
            print(f"   相对误差中位数: {np.median(relative_errors)*100:.2f}%")
            
            # 相对误差分布
            rel_thresholds = [0.001, 0.005, 0.01, 0.02, 0.05, 0.1]  # 0.1%, 0.5%, 1%, 2%, 5%, 10%
            print(f"\n   相对误差分布:")
            for threshold in rel_thresholds:
                count = np.sum(relative_errors <= threshold)
                percentage = count / len(relative_errors) * 100
                print(f"     相对误差 ≤ {threshold*100:4.1f}%: {count:3d}/{len(relative_errors)} ({percentage:5.1f}%)")
        
        # 基于数据动态范围的误差分析
        data_range = original.max() - original.min()
        print(f"\n📏 基于数据动态范围的误差分析:")
        print(f"   数据动态范围: {data_range:.6f}")
        
        # 使用数据动态范围的百分比作为阈值
        range_thresholds = [0.001, 0.005, 0.01, 0.02, 0.05, 0.1]  # 0.1%, 0.5%, 1%, 2%, 5%, 10%
        print(f"   基于动态范围的误差分布:")
        for threshold in range_thresholds:
            abs_threshold = data_range * threshold
            count = np.sum(difference <= abs_threshold)
            percentage = count / difference.size * 100
            print(f"     误差 ≤ {threshold*100:4.1f}%动态范围({abs_threshold:.6f}): {count:3d}/{difference.size} ({percentage:5.1f}%)")
        
        # 信噪比分析
        print(f"\n📡 信噪比分析:")
        signal_power = np.mean(original**2)
        noise_power = np.mean(difference**2)
        snr_db = 10 * np.log10(signal_power / noise_power)
        
        print(f"   信号功率: {signal_power:.6f}")
        print(f"   噪声功率: {noise_power:.6f}")
        print(f"   信噪比(SNR): {snr_db:.2f} dB")
        
        # PSNR分析
        mse = np.mean(difference**2)
        max_val = max(abs(original.max()), abs(original.min()))
        psnr_db = 20 * np.log10(max_val) - 10 * np.log10(mse)
        
        print(f"   均方误差(MSE): {mse:.8f}")
        print(f"   峰值信噪比(PSNR): {psnr_db:.2f} dB")
        
        # 误差热点分析
        print(f"\n🔥 误差热点分析:")
        
        # 找出误差最大的前5个像素
        flat_indices = np.argsort(difference.flatten())[-5:][::-1]  # 降序排列
        positions = [np.unravel_index(idx, difference.shape) for idx in flat_indices]
        
        print(f"   误差最大的5个像素:")
        print(f"   {'位置':>8} {'原始值':>10} {'重建值':>10} {'绝对误差':>10} {'相对误差%':>10}")
        print(f"   {'-'*58}")
        
        for i, (pos, flat_idx) in enumerate(zip(positions, flat_indices)):
            orig_val = original.flat[flat_idx]
            recon_val = reconstructed.flat[flat_idx]
            error_val = difference.flat[flat_idx]
            rel_error = (error_val / abs(orig_val) * 100) if abs(orig_val) > 1e-8 else 0
            print(f"   {str(pos):>8} {orig_val:>10.6f} {recon_val:>10.6f} {error_val:>10.6f} {rel_error:>9.2f}%")
        
        # 误差最小的前5个像素
        flat_indices_min = np.argsort(difference.flatten())[:5]  # 升序排列
        positions_min = [np.unravel_index(idx, difference.shape) for idx in flat_indices_min]
        
        print(f"\n   误差最小的5个像素:")
        print(f"   {'位置':>8} {'原始值':>10} {'重建值':>10} {'绝对误差':>10} {'相对误差%':>10}")
        print(f"   {'-'*58}")
        
        for i, (pos, flat_idx) in enumerate(zip(positions_min, flat_indices_min)):
            orig_val = original.flat[flat_idx]
            recon_val = reconstructed.flat[flat_idx]
            error_val = difference.flat[flat_idx]
            rel_error = (error_val / abs(orig_val) * 100) if abs(orig_val) > 1e-8 else 0
            print(f"   {str(pos):>8} {orig_val:>10.6f} {recon_val:>10.6f} {error_val:>10.6f} {rel_error:>9.2f}%")
        
        # 数值范围分析
        print(f"\n📊 数值范围分析:")
        
        # 将原始值分成几个范围，分析每个范围的误差
        value_ranges = [
            (-np.inf, -2.0),
            (-2.0, -1.0),
            (-1.0, 0.0),
            (0.0, 1.0),
            (1.0, 2.0),
            (2.0, np.inf)
        ]
        
        range_names = ["< -2.0", "[-2.0, -1.0)", "[-1.0, 0.0)", "[0.0, 1.0)", "[1.0, 2.0)", ">= 2.0"]
        
        print(f"   不同数值范围的误差分析:")
        print(f"   {'范围':>12} {'像素数':>8} {'平均绝对误差':>15} {'最大绝对误差':>15} {'平均相对误差%':>15}")
        print(f"   {'-'*75}")
        
        for (min_val, max_val), range_name in zip(value_ranges, range_names):
            if min_val == -np.inf:
                mask = original < max_val
            elif max_val == np.inf:
                mask = original >= min_val
            else:
                mask = (original >= min_val) & (original < max_val)
            
            if np.any(mask):
                range_errors = difference[mask]
                range_originals = original[mask]
                
                avg_error = np.mean(range_errors)
                max_error = np.max(range_errors)
                
                # 计算相对误差
                non_zero_in_range = np.abs(range_originals) > 1e-8
                if np.any(non_zero_in_range):
                    avg_rel_error = np.mean(range_errors[non_zero_in_range] / np.abs(range_originals[non_zero_in_range])) * 100
                else:
                    avg_rel_error = 0.0
                
                print(f"   {range_name:>12} {np.sum(mask):>8} {avg_error:>15.6f} {max_error:>15.6f} {avg_rel_error:>14.2f}%")
        
        # 误差分布直方图统计
        print(f"\n📈 误差分布直方图:")
        hist, bin_edges = np.histogram(difference, bins=10)
        print(f"   误差区间分布:")
        for i in range(len(hist)):
            bin_start = bin_edges[i]
            bin_end = bin_edges[i+1]
            count = hist[i]
            percentage = count / difference.size * 100
            print(f"     [{bin_start:.6f}, {bin_end:.6f}): {count:3d} ({percentage:5.1f}%)")
        
        # 保存修正后的分析报告
        with open("frame0_channel0_corrected_analysis.txt", "w", encoding='utf-8') as f:
            f.write("帧0通道0误差分析报告（修正版）\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"重要说明：\n")
            f.write(f"原始数据和重建数据都是真实特征值，不是0-1归一化数据\n")
            f.write(f"因此不应使用量化步长来评估误差，而应使用相对误差和基于数据范围的误差\n\n")
            
            f.write(f"基本信息:\n")
            f.write(f"  数据形状: {original.shape}\n")
            f.write(f"  总像素数: {original.size}\n")
            f.write(f"  原始数据范围: [{original.min():.6f}, {original.max():.6f}]\n")
            f.write(f"  重建数据范围: [{reconstructed.min():.6f}, {reconstructed.max():.6f}]\n")
            f.write(f"  数据动态范围: {data_range:.6f}\n")
            f.write(f"  平均绝对误差: {difference.mean():.6f}\n")
            f.write(f"  误差标准差: {difference.std():.6f}\n\n")
            
            f.write(f"质量指标:\n")
            f.write(f"  平均相对误差: {relative_errors.mean()*100:.2f}%\n")
            f.write(f"  信噪比(SNR): {snr_db:.2f} dB\n")
            f.write(f"  峰值信噪比(PSNR): {psnr_db:.2f} dB\n")
        
        print(f"\n✅ 修正后的详细分析报告已保存到: frame0_channel0_corrected_analysis.txt")
        
        return {
            'original': original,
            'reconstructed': reconstructed,
            'difference': difference,
            'relative_error_mean': relative_errors.mean() * 100,
            'snr_db': snr_db,
            'psnr_db': psnr_db,
            'data_range': data_range
        }
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = analyze_frame0_channel0_corrected()
    
    if result:
        print(f"\n🎯 关键结论（修正版）:")
        print(f"   平均相对误差: {result['relative_error_mean']:.2f}%")
        print(f"   信噪比(SNR): {result['snr_db']:.2f} dB")
        print(f"   峰值信噪比(PSNR): {result['psnr_db']:.2f} dB")
        print(f"   数据动态范围: {result['data_range']:.6f}")
        print(f"\n💡 说明: 这些指标更适合评估非归一化特征数据的重建质量")
    else:
        print(f"\n❌ 分析失败")
