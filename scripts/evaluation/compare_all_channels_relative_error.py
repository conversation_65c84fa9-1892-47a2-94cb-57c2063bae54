#!/usr/bin/env python3
"""
统计第0帧的所有非resized通道和resized通道的平均相对误差
对比分析两种通道类型的重建质量差异
"""

import numpy as np
import matplotlib.pyplot as plt

def compare_all_channels_relative_error():
    """统计所有通道的平均相对误差"""
    
    print("=== 第0帧所有通道相对误差对比分析 ===")
    
    try:
        # 文件路径
        base_path = "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data"
        encode_base = f"{base_path}/exported_encode_data/frame_0000"
        decode_base = f"{base_path}/exported_decode_data/frame_0000"
        
        # 加载数据
        original_tensor = np.load(f"{encode_base}/original_tensor.npy")
        reconstructed_tensor = np.load(f"{decode_base}/reconstructed_tensor.npy")[0]  # 去掉batch维度
        
        # 加载resize信息
        resize_info = np.load(f"{encode_base}/resize_channel_info.npy", allow_pickle=True).item()
        non_resize_channels = sorted(resize_info['non_resize_channels'])
        resize_channels = sorted(resize_info['resize_channels'])
        
        print(f"✅ 数据加载成功")
        print(f"   原始数据形状: {original_tensor.shape}")
        print(f"   重建数据形状: {reconstructed_tensor.shape}")
        print(f"   非resize通道数: {len(non_resize_channels)}")
        print(f"   resize通道数: {len(resize_channels)}")
        
        # 分析非resize通道
        print(f"\n📊 分析非resize通道...")
        non_resize_errors = []
        non_resize_stats = []
        
        for ch_idx in non_resize_channels:
            original_ch = original_tensor[ch_idx]
            reconstructed_ch = reconstructed_tensor[ch_idx]
            
            # 计算绝对误差
            diff = np.abs(reconstructed_ch - original_ch)
            
            # 计算相对误差
            non_zero_mask = np.abs(original_ch) > 1e-8
            if np.any(non_zero_mask):
                relative_errors = diff[non_zero_mask] / np.abs(original_ch[non_zero_mask])
                mean_relative_error = np.mean(relative_errors) * 100
                median_relative_error = np.median(relative_errors) * 100
                std_relative_error = np.std(relative_errors) * 100
            else:
                mean_relative_error = 0.0
                median_relative_error = 0.0
                std_relative_error = 0.0
            
            # 计算其他指标
            mean_abs_error = np.mean(diff)
            max_abs_error = np.max(diff)
            
            # 计算SNR和PSNR
            signal_power = np.mean(original_ch**2)
            noise_power = np.mean(diff**2)
            if noise_power > 0:
                snr_db = 10 * np.log10(signal_power / noise_power)
                max_val = max(abs(original_ch.max()), abs(original_ch.min()))
                psnr_db = 20 * np.log10(max_val) - 10 * np.log10(noise_power)
            else:
                snr_db = float('inf')
                psnr_db = float('inf')
            
            non_resize_errors.append(mean_relative_error)
            non_resize_stats.append({
                'channel': ch_idx,
                'mean_relative_error': mean_relative_error,
                'median_relative_error': median_relative_error,
                'std_relative_error': std_relative_error,
                'mean_abs_error': mean_abs_error,
                'max_abs_error': max_abs_error,
                'snr_db': snr_db,
                'psnr_db': psnr_db,
                'data_range': [float(original_ch.min()), float(original_ch.max())]
            })
        
        # 分析resize通道
        print(f"📊 分析resize通道...")
        resize_errors = []
        resize_stats = []
        
        for ch_idx in resize_channels:
            original_ch = original_tensor[ch_idx]
            reconstructed_ch = reconstructed_tensor[ch_idx]
            
            # 计算绝对误差
            diff = np.abs(reconstructed_ch - original_ch)
            
            # 计算相对误差
            non_zero_mask = np.abs(original_ch) > 1e-8
            if np.any(non_zero_mask):
                relative_errors = diff[non_zero_mask] / np.abs(original_ch[non_zero_mask])
                mean_relative_error = np.mean(relative_errors) * 100
                median_relative_error = np.median(relative_errors) * 100
                std_relative_error = np.std(relative_errors) * 100
            else:
                mean_relative_error = 0.0
                median_relative_error = 0.0
                std_relative_error = 0.0
            
            # 计算其他指标
            mean_abs_error = np.mean(diff)
            max_abs_error = np.max(diff)
            
            # 计算SNR和PSNR
            signal_power = np.mean(original_ch**2)
            noise_power = np.mean(diff**2)
            if noise_power > 0:
                snr_db = 10 * np.log10(signal_power / noise_power)
                max_val = max(abs(original_ch.max()), abs(original_ch.min()))
                psnr_db = 20 * np.log10(max_val) - 10 * np.log10(noise_power)
            else:
                snr_db = float('inf')
                psnr_db = float('inf')
            
            resize_errors.append(mean_relative_error)
            resize_stats.append({
                'channel': ch_idx,
                'mean_relative_error': mean_relative_error,
                'median_relative_error': median_relative_error,
                'std_relative_error': std_relative_error,
                'mean_abs_error': mean_abs_error,
                'max_abs_error': max_abs_error,
                'snr_db': snr_db,
                'psnr_db': psnr_db,
                'data_range': [float(original_ch.min()), float(original_ch.max())]
            })
        
        # 统计结果
        print(f"\n📈 统计结果对比:")
        
        # 非resize通道统计
        non_resize_mean = np.mean(non_resize_errors)
        non_resize_median = np.median(non_resize_errors)
        non_resize_std = np.std(non_resize_errors)
        non_resize_min = np.min(non_resize_errors)
        non_resize_max = np.max(non_resize_errors)
        
        print(f"\n🔵 非resize通道 ({len(non_resize_channels)}个):")
        print(f"   平均相对误差: {non_resize_mean:.2f}%")
        print(f"   中位数相对误差: {non_resize_median:.2f}%")
        print(f"   标准差: {non_resize_std:.2f}%")
        print(f"   范围: [{non_resize_min:.2f}%, {non_resize_max:.2f}%]")
        
        # resize通道统计
        resize_mean = np.mean(resize_errors)
        resize_median = np.median(resize_errors)
        resize_std = np.std(resize_errors)
        resize_min = np.min(resize_errors)
        resize_max = np.max(resize_errors)
        
        print(f"\n🔴 resize通道 ({len(resize_channels)}个):")
        print(f"   平均相对误差: {resize_mean:.2f}%")
        print(f"   中位数相对误差: {resize_median:.2f}%")
        print(f"   标准差: {resize_std:.2f}%")
        print(f"   范围: [{resize_min:.2f}%, {resize_max:.2f}%]")
        
        # 对比分析
        print(f"\n⚖️  对比分析:")
        diff_mean = resize_mean - non_resize_mean
        diff_median = resize_median - non_resize_median
        ratio_mean = resize_mean / non_resize_mean if non_resize_mean > 0 else float('inf')
        
        print(f"   平均相对误差差异: {diff_mean:+.2f}% (resize - non_resize)")
        print(f"   中位数相对误差差异: {diff_median:+.2f}%")
        print(f"   resize通道误差是非resize的: {ratio_mean:.2f}倍")
        
        if diff_mean > 0:
            print(f"   ❌ resize通道的相对误差更大")
        else:
            print(f"   ✅ resize通道的相对误差更小")
        
        # 显示最好和最差的通道
        print(f"\n🏆 最好的通道 (相对误差最小):")
        
        # 非resize通道最好的5个
        best_non_resize = sorted(non_resize_stats, key=lambda x: x['mean_relative_error'])[:5]
        print(f"   非resize通道:")
        for i, stat in enumerate(best_non_resize):
            print(f"     {i+1}. 通道{stat['channel']:3d}: {stat['mean_relative_error']:5.2f}% (SNR: {stat['snr_db']:5.1f}dB)")
        
        # resize通道最好的5个
        best_resize = sorted(resize_stats, key=lambda x: x['mean_relative_error'])[:5]
        print(f"   resize通道:")
        for i, stat in enumerate(best_resize):
            print(f"     {i+1}. 通道{stat['channel']:3d}: {stat['mean_relative_error']:5.2f}% (SNR: {stat['snr_db']:5.1f}dB)")
        
        print(f"\n💔 最差的通道 (相对误差最大):")
        
        # 非resize通道最差的5个
        worst_non_resize = sorted(non_resize_stats, key=lambda x: x['mean_relative_error'], reverse=True)[:5]
        print(f"   非resize通道:")
        for i, stat in enumerate(worst_non_resize):
            print(f"     {i+1}. 通道{stat['channel']:3d}: {stat['mean_relative_error']:5.2f}% (SNR: {stat['snr_db']:5.1f}dB)")
        
        # resize通道最差的5个
        worst_resize = sorted(resize_stats, key=lambda x: x['mean_relative_error'], reverse=True)[:5]
        print(f"   resize通道:")
        for i, stat in enumerate(worst_resize):
            print(f"     {i+1}. 通道{stat['channel']:3d}: {stat['mean_relative_error']:5.2f}% (SNR: {stat['snr_db']:5.1f}dB)")
        
        # 计算平均SNR和PSNR
        non_resize_snr = np.mean([s['snr_db'] for s in non_resize_stats if s['snr_db'] != float('inf')])
        resize_snr = np.mean([s['snr_db'] for s in resize_stats if s['snr_db'] != float('inf')])
        
        non_resize_psnr = np.mean([s['psnr_db'] for s in non_resize_stats if s['psnr_db'] != float('inf')])
        resize_psnr = np.mean([s['psnr_db'] for s in resize_stats if s['psnr_db'] != float('inf')])
        
        print(f"\n📡 信号质量对比:")
        print(f"   非resize通道平均SNR: {non_resize_snr:.2f} dB")
        print(f"   resize通道平均SNR: {resize_snr:.2f} dB")
        print(f"   SNR差异: {resize_snr - non_resize_snr:+.2f} dB")
        print(f"   非resize通道平均PSNR: {non_resize_psnr:.2f} dB")
        print(f"   resize通道平均PSNR: {resize_psnr:.2f} dB")
        print(f"   PSNR差异: {resize_psnr - non_resize_psnr:+.2f} dB")
        
        # 保存详细报告
        with open("all_channels_relative_error_comparison.txt", "w", encoding='utf-8') as f:
            f.write("第0帧所有通道相对误差对比分析报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"数据概况:\n")
            f.write(f"  非resize通道数: {len(non_resize_channels)}\n")
            f.write(f"  resize通道数: {len(resize_channels)}\n")
            f.write(f"  总通道数: {len(non_resize_channels) + len(resize_channels)}\n\n")
            
            f.write(f"相对误差统计:\n")
            f.write(f"  非resize通道平均相对误差: {non_resize_mean:.2f}%\n")
            f.write(f"  resize通道平均相对误差: {resize_mean:.2f}%\n")
            f.write(f"  差异: {diff_mean:+.2f}%\n")
            f.write(f"  倍数关系: {ratio_mean:.2f}倍\n\n")
            
            f.write(f"信号质量对比:\n")
            f.write(f"  非resize通道平均SNR: {non_resize_snr:.2f} dB\n")
            f.write(f"  resize通道平均SNR: {resize_snr:.2f} dB\n")
            f.write(f"  非resize通道平均PSNR: {non_resize_psnr:.2f} dB\n")
            f.write(f"  resize通道平均PSNR: {resize_psnr:.2f} dB\n")
        
        print(f"\n✅ 详细报告已保存到: all_channels_relative_error_comparison.txt")
        
        return {
            'non_resize_mean_error': non_resize_mean,
            'resize_mean_error': resize_mean,
            'non_resize_stats': non_resize_stats,
            'resize_stats': resize_stats,
            'non_resize_snr': non_resize_snr,
            'resize_snr': resize_snr
        }
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = compare_all_channels_relative_error()
    
    if result:
        print(f"\n🎯 关键结论:")
        print(f"   非resize通道平均相对误差: {result['non_resize_mean_error']:.2f}%")
        print(f"   resize通道平均相对误差: {result['resize_mean_error']:.2f}%")
        print(f"   resize通道误差是非resize的: {result['resize_mean_error']/result['non_resize_mean_error']:.2f}倍")
    else:
        print(f"\n❌ 分析失败")
