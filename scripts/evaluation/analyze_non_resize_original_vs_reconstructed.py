#!/usr/bin/env python3
"""
分析非resize通道：编码端原始数据 vs 解码端重建数据
完整端到端流程验证：原始 → 归一化 → 量化 → 打包 → 传输 → 解包 → 逆量化 → 逆归一化 → 重建
"""

import numpy as np

def analyze_non_resize_original_vs_reconstructed():
    """分析非resize通道的原始数据和重建数据对比"""
    
    print("=== 分析非resize通道：原始数据 vs 重建数据 ===")
    print("完整端到端流程：原始 → 归一化 → 量化 → 打包 → 传输 → 解包 → 逆量化 → 逆归一化 → 重建")
    
    # 文件路径
    encode_base = "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_encode_data/frame_0000"
    decode_base = "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0000"
    
    try:
        # 加载数据
        original_tensor = np.load(f"{encode_base}/original_tensor.npy")
        reconstructed_tensor = np.load(f"{decode_base}/reconstructed_tensor.npy")[0]  # 去掉batch维度
        
        # 加载resize信息
        resize_info = np.load(f"{encode_base}/resize_channel_info.npy", allow_pickle=True).item()
        non_resize_channels = resize_info['non_resize_channels']
        resize_channels = resize_info['resize_channels']
        
        print(f"✅ 数据加载成功")
        print(f"   原始数据形状: {original_tensor.shape}")
        print(f"   重建数据形状: {reconstructed_tensor.shape}")
        print(f"   非resize通道数: {len(non_resize_channels)}")
        print(f"   resize通道数: {len(resize_channels)}")
        
        # 量化参数
        bit_depth = 10
        max_levels = 2**bit_depth - 1  # 1023
        quantization_step = 1.0 / max_levels
        half_step_threshold = quantization_step / 2
        
        print(f"\n📏 量化参数:")
        print(f"   量化位深: {bit_depth} bit")
        print(f"   量化级别: {max_levels + 1}")
        print(f"   量化步长: {quantization_step:.8f}")
        print(f"   1/2量化步长阈值: {half_step_threshold:.8f}")
        
        # 验证数据范围
        print(f"\n🔍 数据范围验证:")
        print(f"   原始数据范围: [{original_tensor.min():.6f}, {original_tensor.max():.6f}]")
        print(f"   重建数据范围: [{reconstructed_tensor.min():.6f}, {reconstructed_tensor.max():.6f}]")
        
        # 分析非resize通道
        print(f"\n🔍 分析非resize通道的原始vs重建对比:")
        
        total_pixels = 0
        within_half_step_pixels = 0
        within_one_step_pixels = 0
        within_two_step_pixels = 0
        channel_stats = []
        
        print(f"{'通道':>4} {'最大差异':>12} {'平均差异':>12} {'相对误差%':>12} {'1/2步长准确率':>15} {'1步长准确率':>13} {'状态':>8}")
        print("-" * 95)
        
        for ch_idx in non_resize_channels:
            # 获取通道数据
            original_ch = original_tensor[ch_idx]
            reconstructed_ch = reconstructed_tensor[ch_idx]
            
            # 计算差异
            diff = np.abs(reconstructed_ch - original_ch)
            
            # 计算相对误差百分比
            # 避免除零，使用非零像素计算相对误差
            non_zero_mask = np.abs(original_ch) > 1e-8  # 避免除以接近零的值
            if np.any(non_zero_mask):
                relative_error_percent = np.mean(diff[non_zero_mask] / np.abs(original_ch[non_zero_mask])) * 100
            else:
                relative_error_percent = 0.0
            
            # 统计像素数
            ch_total_pixels = diff.size
            ch_within_half_step = np.sum(diff <= half_step_threshold)
            ch_within_one_step = np.sum(diff <= quantization_step)
            ch_within_two_step = np.sum(diff <= quantization_step * 2)
            
            ch_half_percentage = ch_within_half_step / ch_total_pixels * 100
            ch_one_percentage = ch_within_one_step / ch_total_pixels * 100
            ch_two_percentage = ch_within_two_step / ch_total_pixels * 100
            
            max_error = np.max(diff)
            mean_error = np.mean(diff)
            
            # 判断状态
            if ch_half_percentage >= 95:
                status = "✅优秀"
            elif ch_half_percentage >= 80:
                status = "🟡良好"
            elif ch_half_percentage >= 50:
                status = "🟠一般"
            else:
                status = "❌较差"
            
            # 统计信息
            ch_stats = {
                'channel': ch_idx,
                'total_pixels': ch_total_pixels,
                'within_half_step': ch_within_half_step,
                'within_one_step': ch_within_one_step,
                'within_two_step': ch_within_two_step,
                'half_percentage': ch_half_percentage,
                'one_percentage': ch_one_percentage,
                'two_percentage': ch_two_percentage,
                'max_error': max_error,
                'mean_error': mean_error,
                'relative_error_percent': relative_error_percent
            }
            channel_stats.append(ch_stats)
            
            # 累计统计
            total_pixels += ch_total_pixels
            within_half_step_pixels += ch_within_half_step
            within_one_step_pixels += ch_within_one_step
            within_two_step_pixels += ch_within_two_step
            
            # 显示前20个通道的详细信息
            if len(channel_stats) <= 20:
                print(f"{ch_idx:4d} {max_error:12.6f} {mean_error:12.6f} {relative_error_percent:10.1f}% {ch_half_percentage:13.1f}% {ch_one_percentage:11.1f}% {status:>8}")
        
        # 总体统计
        overall_half_percentage = within_half_step_pixels / total_pixels * 100
        overall_one_percentage = within_one_step_pixels / total_pixels * 100
        overall_two_percentage = within_two_step_pixels / total_pixels * 100
        
        print(f"\n📊 总体统计结果:")
        print(f"   非resize通道数: {len(non_resize_channels)}")
        print(f"   总像素数: {total_pixels:,}")
        print(f"   小于1/2量化步长的像素数: {within_half_step_pixels:,}")
        print(f"   1/2量化步长准确率: {overall_half_percentage:.2f}%")
        print(f"   1量化步长准确率: {overall_one_percentage:.2f}%")
        print(f"   2量化步长准确率: {overall_two_percentage:.2f}%")
        
        # 通道级别统计
        half_percentages = [stat['half_percentage'] for stat in channel_stats]
        one_percentages = [stat['one_percentage'] for stat in channel_stats]
        relative_errors = [stat['relative_error_percent'] for stat in channel_stats]
        
        excellent_channels = [stat for stat in channel_stats if stat['half_percentage'] >= 95]
        good_channels = [stat for stat in channel_stats if 80 <= stat['half_percentage'] < 95]
        fair_channels = [stat for stat in channel_stats if 50 <= stat['half_percentage'] < 80]
        poor_channels = [stat for stat in channel_stats if stat['half_percentage'] < 50]
        
        print(f"\n📈 通道级别统计（1/2量化步长）:")
        print(f"   优秀通道 (≥95%): {len(excellent_channels)} / {len(non_resize_channels)}")
        print(f"   良好通道 (80-95%): {len(good_channels)} / {len(non_resize_channels)}")
        print(f"   一般通道 (50-80%): {len(fair_channels)} / {len(non_resize_channels)}")
        print(f"   较差通道 (<50%): {len(poor_channels)} / {len(non_resize_channels)}")
        print(f"   平均通道准确率: {np.mean(half_percentages):.2f}%")
        print(f"   中位数通道准确率: {np.median(half_percentages):.2f}%")
        print(f"   平均相对误差: {np.mean(relative_errors):.2f}%")
        print(f"   中位数相对误差: {np.median(relative_errors):.2f}%")
        
        # 显示最好和最差的通道
        best_channels = sorted(channel_stats, key=lambda x: x['half_percentage'], reverse=True)[:5]
        worst_channels = sorted(channel_stats, key=lambda x: x['half_percentage'])[:5]
        
        print(f"\n🏆 最好的5个非resize通道:")
        for stat in best_channels:
            print(f"   通道{stat['channel']:3d}: {stat['half_percentage']:5.1f}% (最大误差={stat['max_error']:.6f}, 相对误差={stat['relative_error_percent']:.1f}%)")
        
        print(f"\n💔 最差的5个非resize通道:")
        for stat in worst_channels:
            print(f"   通道{stat['channel']:3d}: {stat['half_percentage']:5.1f}% (最大误差={stat['max_error']:.6f}, 相对误差={stat['relative_error_percent']:.1f}%)")
        
        # 与之前分析对比
        print(f"\n🔄 与之前分析对比:")
        print(f"   归一化vs解包准确率: 93.12% (来自之前分析)")
        print(f"   原始vs重建准确率: {overall_half_percentage:.2f}%")
        print(f"   差异: {93.12 - overall_half_percentage:.2f}个百分点")
        
        if overall_half_percentage < 93.12:
            print(f"   ✅ 符合预期：端到端流程存在累积误差")
        else:
            print(f"   ⚠️  意外：端到端准确率不低于中间步骤")
        
        # 误差分布分析
        print(f"\n📊 误差分布分析:")
        
        all_errors = []
        all_relative_errors = []
        for ch_idx in non_resize_channels:
            original_ch = original_tensor[ch_idx]
            reconstructed_ch = reconstructed_tensor[ch_idx]
            diff = np.abs(reconstructed_ch - original_ch)
            all_errors.extend(diff.flatten())
            
            # 计算相对误差
            non_zero_mask = np.abs(original_ch) > 1e-8
            if np.any(non_zero_mask):
                rel_errors = diff[non_zero_mask] / np.abs(original_ch[non_zero_mask])
                all_relative_errors.extend(rel_errors.flatten())
        
        all_errors = np.array(all_errors)
        all_relative_errors = np.array(all_relative_errors)
        
        # 不同误差阈值的统计
        thresholds = [
            half_step_threshold,
            quantization_step,
            quantization_step * 2,
            quantization_step * 5,
            quantization_step * 10,
            quantization_step * 20
        ]
        
        print(f"   绝对误差分布:")
        for threshold in thresholds:
            count = np.sum(all_errors <= threshold)
            percentage = count / len(all_errors) * 100
            threshold_name = f"{threshold/quantization_step:.1f}x量化步长"
            print(f"     误差 ≤ {threshold_name}: {percentage:5.1f}%")
        
        print(f"   相对误差分布:")
        rel_thresholds = [0.01, 0.02, 0.05, 0.1, 0.2, 0.5]  # 1%, 2%, 5%, 10%, 20%, 50%
        for threshold in rel_thresholds:
            count = np.sum(all_relative_errors <= threshold)
            percentage = count / len(all_relative_errors) * 100
            print(f"     相对误差 ≤ {threshold*100:4.1f}%: {percentage:5.1f}%")
        
        # 保存详细报告
        with open("non_resize_original_vs_reconstructed_analysis.txt", "w") as f:
            f.write("非resize通道原始vs重建数据分析报告\n")
            f.write("=" * 80 + "\n\n")
            f.write("完整端到端流程：原始 → 归一化 → 量化 → 打包 → 传输 → 解包 → 逆量化 → 逆归一化 → 重建\n\n")
            
            f.write(f"总体统计:\n")
            f.write(f"  非resize通道数: {len(non_resize_channels)}\n")
            f.write(f"  总像素数: {total_pixels:,}\n")
            f.write(f"  小于1/2量化步长的像素数: {within_half_step_pixels:,}\n")
            f.write(f"  1/2量化步长准确率: {overall_half_percentage:.2f}%\n")
            f.write(f"  1量化步长准确率: {overall_one_percentage:.2f}%\n")
            f.write(f"  2量化步长准确率: {overall_two_percentage:.2f}%\n")
            f.write(f"  平均相对误差: {np.mean(relative_errors):.2f}%\n\n")
            
            f.write(f"通道级别统计:\n")
            f.write(f"  优秀通道 (≥95%): {len(excellent_channels)}\n")
            f.write(f"  良好通道 (80-95%): {len(good_channels)}\n")
            f.write(f"  一般通道 (50-80%): {len(fair_channels)}\n")
            f.write(f"  较差通道 (<50%): {len(poor_channels)}\n")
            f.write(f"  平均通道准确率: {np.mean(half_percentages):.2f}%\n\n")
            
            f.write(f"详细通道统计:\n")
            for stat in sorted(channel_stats, key=lambda x: x['half_percentage'], reverse=True):
                f.write(f"  通道{stat['channel']:3d}: 1/2步长={stat['half_percentage']:5.1f}%, "
                       f"1步长={stat['one_percentage']:5.1f}%, 2步长={stat['two_percentage']:5.1f}%, "
                       f"最大误差={stat['max_error']:.6f}, 相对误差={stat['relative_error_percent']:.1f}%\n")
            
            f.write(f"\n与中间步骤对比:\n")
            f.write(f"  归一化vs解包准确率: 93.12%\n")
            f.write(f"  原始vs重建准确率: {overall_half_percentage:.2f}%\n")
            f.write(f"  差异: {93.12 - overall_half_percentage:.2f}个百分点\n")
        
        print(f"\n✅ 详细报告已保存到: non_resize_original_vs_reconstructed_analysis.txt")
        
        return {
            'overall_half_percentage': overall_half_percentage,
            'overall_one_percentage': overall_one_percentage,
            'excellent_channels': len(excellent_channels),
            'good_channels': len(good_channels),
            'fair_channels': len(fair_channels),
            'poor_channels': len(poor_channels),
            'average_relative_error': np.mean(relative_errors),
            'channel_stats': channel_stats
        }
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = analyze_non_resize_original_vs_reconstructed()
    
    if result:
        print(f"\n🎯 关键结论: 非resize通道端到端准确率为 {result['overall_half_percentage']:.2f}%")
        print(f"🎯 平均相对误差: {result['average_relative_error']:.2f}%")
    else:
        print(f"\n❌ 分析失败")
