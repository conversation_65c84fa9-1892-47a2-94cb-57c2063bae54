#!/usr/bin/env python3
"""
导出前10个非resize通道的原始数据和重建数据
用于详细分析量化/逆量化过程的精度损失
"""

import numpy as np
import os

def export_first10_non_resize_channels():
    """导出前10个非resize通道的原始数据和重建数据"""
    
    print("=== 导出前10个非resize通道的原始数据和重建数据 ===")
    
    # 文件路径
    base_path = "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data"
    encode_base = f"{base_path}/exported_encode_data/frame_0000"
    decode_base = f"{base_path}/exported_decode_data/frame_0000"
    
    try:
        # 加载数据
        original_tensor = np.load(f"{encode_base}/original_tensor.npy")
        reconstructed_tensor = np.load(f"{decode_base}/reconstructed_tensor.npy")[0]  # 去掉batch维度
        
        # 加载resize信息
        resize_info = np.load(f"{encode_base}/resize_channel_info.npy", allow_pickle=True).item()
        non_resize_channels = resize_info['non_resize_channels']
        resize_channels = resize_info['resize_channels']
        
        print(f"✅ 数据加载成功")
        print(f"   原始数据形状: {original_tensor.shape}")
        print(f"   重建数据形状: {reconstructed_tensor.shape}")
        print(f"   非resize通道数: {len(non_resize_channels)}")
        print(f"   resize通道数: {len(resize_channels)}")
        
        # 获取前10个非resize通道
        first_10_non_resize = sorted(non_resize_channels)[:10]
        print(f"\n📋 前10个非resize通道: {first_10_non_resize}")
        
        # 创建导出目录
        export_dir = "exported_first10_non_resize_channels"
        os.makedirs(export_dir, exist_ok=True)
        
        # 导出每个通道的数据
        for i, ch_idx in enumerate(first_10_non_resize):
            # 获取通道数据
            original_ch = original_tensor[ch_idx]
            reconstructed_ch = reconstructed_tensor[ch_idx]
            
            # 计算差异
            diff = np.abs(reconstructed_ch - original_ch)
            
            # 计算统计信息
            max_error = np.max(diff)
            mean_error = np.mean(diff)
            
            # 计算相对误差
            non_zero_mask = np.abs(original_ch) > 1e-8
            if np.any(non_zero_mask):
                relative_error_percent = np.mean(diff[non_zero_mask] / np.abs(original_ch[non_zero_mask])) * 100
            else:
                relative_error_percent = 0.0
            
            # 量化参数
            quantization_step = 1.0 / 1023
            half_step_threshold = quantization_step / 2
            
            # 计算准确率
            within_half_step = np.sum(diff <= half_step_threshold)
            total_pixels = diff.size
            accuracy = within_half_step / total_pixels * 100
            
            print(f"\n📊 通道 {ch_idx} (第{i+1}个非resize通道):")
            print(f"   数据形状: {original_ch.shape}")
            print(f"   原始数据范围: [{original_ch.min():.6f}, {original_ch.max():.6f}]")
            print(f"   重建数据范围: [{reconstructed_ch.min():.6f}, {reconstructed_ch.max():.6f}]")
            print(f"   最大误差: {max_error:.6f}")
            print(f"   平均误差: {mean_error:.6f}")
            print(f"   相对误差: {relative_error_percent:.2f}%")
            print(f"   1/2量化步长准确率: {accuracy:.2f}%")
            
            # 保存原始数据
            original_filename = f"{export_dir}/channel_{ch_idx:03d}_original.npy"
            np.save(original_filename, original_ch)
            
            # 保存重建数据
            reconstructed_filename = f"{export_dir}/channel_{ch_idx:03d}_reconstructed.npy"
            np.save(reconstructed_filename, reconstructed_ch)
            
            # 保存差异数据
            diff_filename = f"{export_dir}/channel_{ch_idx:03d}_difference.npy"
            np.save(diff_filename, diff)
            
            # 保存统计信息
            stats = {
                'channel_index': ch_idx,
                'shape': original_ch.shape,
                'original_range': [float(original_ch.min()), float(original_ch.max())],
                'reconstructed_range': [float(reconstructed_ch.min()), float(reconstructed_ch.max())],
                'max_error': float(max_error),
                'mean_error': float(mean_error),
                'relative_error_percent': float(relative_error_percent),
                'accuracy_half_step': float(accuracy),
                'total_pixels': int(total_pixels),
                'within_half_step_pixels': int(within_half_step)
            }
            
            stats_filename = f"{export_dir}/channel_{ch_idx:03d}_stats.npy"
            np.save(stats_filename, stats)
            
            print(f"   ✅ 已保存: {original_filename}")
            print(f"   ✅ 已保存: {reconstructed_filename}")
            print(f"   ✅ 已保存: {diff_filename}")
            print(f"   ✅ 已保存: {stats_filename}")
        
        # 创建汇总数据
        summary_data = {
            'first_10_non_resize_channels': first_10_non_resize,
            'total_non_resize_channels': len(non_resize_channels),
            'total_resize_channels': len(resize_channels),
            'original_tensor_shape': original_tensor.shape,
            'reconstructed_tensor_shape': reconstructed_tensor.shape,
            'quantization_step': quantization_step,
            'half_step_threshold': half_step_threshold
        }
        
        # 保存汇总信息
        summary_filename = f"{export_dir}/summary_info.npy"
        np.save(summary_filename, summary_data)
        
        # 创建README文件
        readme_content = f"""# 前10个非resize通道数据导出

## 文件说明

### 数据文件
- `channel_XXX_original.npy`: 通道XXX的原始数据 (形状: {original_tensor[0].shape})
- `channel_XXX_reconstructed.npy`: 通道XXX的重建数据 (形状: {reconstructed_tensor[0].shape})
- `channel_XXX_difference.npy`: 通道XXX的差异数据 (|重建 - 原始|)
- `channel_XXX_stats.npy`: 通道XXX的统计信息

### 汇总文件
- `summary_info.npy`: 汇总信息，包含通道列表、形状等基本信息

## 导出的通道
前10个非resize通道: {first_10_non_resize}

## 量化参数
- 量化步长: {quantization_step:.8f}
- 1/2量化步长阈值: {half_step_threshold:.8f}

## 数据范围
- 原始数据范围: [{original_tensor.min():.6f}, {original_tensor.max():.6f}]
- 重建数据范围: [{reconstructed_tensor.min():.6f}, {reconstructed_tensor.max():.6f}]

## 使用示例

```python
import numpy as np

# 加载通道0的数据
original = np.load('channel_000_original.npy')
reconstructed = np.load('channel_000_reconstructed.npy')
difference = np.load('channel_000_difference.npy')
stats = np.load('channel_000_stats.npy', allow_pickle=True).item()

print(f"通道0统计信息: {{stats}}")
```

## 分析目的
这些数据用于详细分析FCTM编解码器中量化/逆量化过程的精度损失问题。
通过对比原始数据和重建数据，可以定位具体的误差来源。
"""
        
        readme_filename = f"{export_dir}/README.md"
        with open(readme_filename, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"\n✅ 数据导出完成!")
        print(f"   导出目录: {export_dir}/")
        print(f"   导出通道: {first_10_non_resize}")
        print(f"   文件数量: {len(first_10_non_resize) * 4 + 2} 个文件")
        print(f"   README文件: {readme_filename}")
        
        # 显示目录内容
        print(f"\n📁 导出文件列表:")
        for filename in sorted(os.listdir(export_dir)):
            filepath = os.path.join(export_dir, filename)
            if os.path.isfile(filepath):
                size = os.path.getsize(filepath)
                print(f"   {filename} ({size:,} bytes)")
        
        return export_dir
        
    except Exception as e:
        print(f"❌ 导出失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    export_dir = export_first10_non_resize_channels()
    
    if export_dir:
        print(f"\n🎯 导出成功! 数据保存在: {export_dir}/")
        print(f"🎯 可以使用这些数据进行详细的量化误差分析")
    else:
        print(f"\n❌ 导出失败")
