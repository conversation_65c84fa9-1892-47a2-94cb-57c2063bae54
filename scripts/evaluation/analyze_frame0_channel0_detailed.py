#!/usr/bin/env python3
"""
详细分析帧0通道0的原始特征和重建特征的误差
像素级别的深入分析
"""

import numpy as np
import matplotlib.pyplot as plt

def analyze_frame0_channel0_detailed():
    """详细分析帧0通道0的误差"""
    
    print("=== 帧0通道0详细误差分析 ===")
    
    try:
        # 加载通道0的数据
        original = np.load('exported_first10_non_resize_channels/channel_000_original.npy')
        reconstructed = np.load('exported_first10_non_resize_channels/channel_000_reconstructed.npy')
        difference = np.load('exported_first10_non_resize_channels/channel_000_difference.npy')
        stats = np.load('exported_first10_non_resize_channels/channel_000_stats.npy', allow_pickle=True).item()
        
        print(f"✅ 数据加载成功")
        print(f"   数据形状: {original.shape}")
        print(f"   总像素数: {original.size}")
        
        # 基本统计信息
        print(f"\n📊 基本统计信息:")
        print(f"   原始数据范围: [{original.min():.6f}, {original.max():.6f}]")
        print(f"   重建数据范围: [{reconstructed.min():.6f}, {reconstructed.max():.6f}]")
        print(f"   差异范围: [{difference.min():.6f}, {difference.max():.6f}]")
        print(f"   平均绝对误差: {difference.mean():.6f}")
        print(f"   误差标准差: {difference.std():.6f}")
        
        # 量化参数
        quantization_step = 1.0 / 1023
        half_step_threshold = quantization_step / 2
        
        print(f"\n📏 量化参数:")
        print(f"   量化步长: {quantization_step:.8f}")
        print(f"   1/2量化步长阈值: {half_step_threshold:.8f}")
        
        # 误差分布统计
        print(f"\n📈 误差分布统计:")
        
        # 不同阈值的统计
        thresholds = [
            half_step_threshold,
            quantization_step,
            quantization_step * 2,
            quantization_step * 5,
            quantization_step * 10
        ]
        
        threshold_names = ["0.5x量化步长", "1.0x量化步长", "2.0x量化步长", "5.0x量化步长", "10.0x量化步长"]
        
        for threshold, name in zip(thresholds, threshold_names):
            count = np.sum(difference <= threshold)
            percentage = count / difference.size * 100
            print(f"   误差 ≤ {name}: {count:3d}/{difference.size} ({percentage:5.1f}%)")
        
        # 相对误差分析
        print(f"\n🔍 相对误差分析:")
        non_zero_mask = np.abs(original) > 1e-8
        if np.any(non_zero_mask):
            relative_errors = difference[non_zero_mask] / np.abs(original[non_zero_mask])
            print(f"   非零像素数: {np.sum(non_zero_mask)}")
            print(f"   相对误差范围: [{relative_errors.min()*100:.2f}%, {relative_errors.max()*100:.2f}%]")
            print(f"   平均相对误差: {relative_errors.mean()*100:.2f}%")
            print(f"   相对误差标准差: {relative_errors.std()*100:.2f}%")
            
            # 相对误差分布
            rel_thresholds = [0.01, 0.02, 0.05, 0.1, 0.2]  # 1%, 2%, 5%, 10%, 20%
            for threshold in rel_thresholds:
                count = np.sum(relative_errors <= threshold)
                percentage = count / len(relative_errors) * 100
                print(f"   相对误差 ≤ {threshold*100:4.1f}%: {count:3d}/{len(relative_errors)} ({percentage:5.1f}%)")
        
        # 像素级详细分析
        print(f"\n🔬 像素级详细分析:")
        
        # 找出最大误差的位置
        max_error_pos = np.unravel_index(np.argmax(difference), difference.shape)
        max_error_value = difference[max_error_pos]
        max_error_original = original[max_error_pos]
        max_error_reconstructed = reconstructed[max_error_pos]
        
        print(f"   最大误差位置: {max_error_pos}")
        print(f"   最大误差值: {max_error_value:.6f}")
        print(f"   该位置原始值: {max_error_original:.6f}")
        print(f"   该位置重建值: {max_error_reconstructed:.6f}")
        print(f"   该位置相对误差: {(max_error_value/abs(max_error_original)*100):.2f}%")
        
        # 找出最小误差的位置
        min_error_pos = np.unravel_index(np.argmin(difference), difference.shape)
        min_error_value = difference[min_error_pos]
        min_error_original = original[min_error_pos]
        min_error_reconstructed = reconstructed[min_error_pos]
        
        print(f"   最小误差位置: {min_error_pos}")
        print(f"   最小误差值: {min_error_value:.6f}")
        print(f"   该位置原始值: {min_error_original:.6f}")
        print(f"   该位置重建值: {min_error_reconstructed:.6f}")
        if abs(min_error_original) > 1e-8:
            print(f"   该位置相对误差: {(min_error_value/abs(min_error_original)*100):.2f}%")
        
        # 误差热点分析
        print(f"\n🔥 误差热点分析:")
        
        # 找出误差最大的前10个像素
        flat_indices = np.argsort(difference.flatten())[-10:][::-1]  # 降序排列
        positions = [np.unravel_index(idx, difference.shape) for idx in flat_indices]
        
        print(f"   误差最大的10个像素:")
        print(f"   {'位置':>8} {'原始值':>10} {'重建值':>10} {'误差':>10} {'相对误差%':>10}")
        print(f"   {'-'*58}")
        
        for i, (pos, flat_idx) in enumerate(zip(positions, flat_indices)):
            orig_val = original.flat[flat_idx]
            recon_val = reconstructed.flat[flat_idx]
            error_val = difference.flat[flat_idx]
            rel_error = (error_val / abs(orig_val) * 100) if abs(orig_val) > 1e-8 else 0
            print(f"   {str(pos):>8} {orig_val:>10.6f} {recon_val:>10.6f} {error_val:>10.6f} {rel_error:>9.2f}%")
        
        # 误差最小的前10个像素
        flat_indices_min = np.argsort(difference.flatten())[:10]  # 升序排列
        positions_min = [np.unravel_index(idx, difference.shape) for idx in flat_indices_min]
        
        print(f"\n   误差最小的10个像素:")
        print(f"   {'位置':>8} {'原始值':>10} {'重建值':>10} {'误差':>10} {'相对误差%':>10}")
        print(f"   {'-'*58}")
        
        for i, (pos, flat_idx) in enumerate(zip(positions_min, flat_indices_min)):
            orig_val = original.flat[flat_idx]
            recon_val = reconstructed.flat[flat_idx]
            error_val = difference.flat[flat_idx]
            rel_error = (error_val / abs(orig_val) * 100) if abs(orig_val) > 1e-8 else 0
            print(f"   {str(pos):>8} {orig_val:>10.6f} {recon_val:>10.6f} {error_val:>10.6f} {rel_error:>9.2f}%")
        
        # 空间分布分析
        print(f"\n🗺️  空间分布分析:")
        
        # 按行分析
        row_errors = np.mean(difference, axis=1)
        max_row = np.argmax(row_errors)
        min_row = np.argmin(row_errors)
        
        print(f"   按行分析:")
        print(f"     误差最大的行: 第{max_row}行, 平均误差={row_errors[max_row]:.6f}")
        print(f"     误差最小的行: 第{min_row}行, 平均误差={row_errors[min_row]:.6f}")
        print(f"     行间误差标准差: {row_errors.std():.6f}")
        
        # 按列分析
        col_errors = np.mean(difference, axis=0)
        max_col = np.argmax(col_errors)
        min_col = np.argmin(col_errors)
        
        print(f"   按列分析:")
        print(f"     误差最大的列: 第{max_col}列, 平均误差={col_errors[max_col]:.6f}")
        print(f"     误差最小的列: 第{min_col}列, 平均误差={col_errors[min_col]:.6f}")
        print(f"     列间误差标准差: {col_errors.std():.6f}")
        
        # 数值范围分析
        print(f"\n📊 数值范围分析:")
        
        # 将原始值分成几个范围，分析每个范围的误差
        value_ranges = [
            (-np.inf, -2.0),
            (-2.0, -1.0),
            (-1.0, 0.0),
            (0.0, 1.0),
            (1.0, 2.0),
            (2.0, np.inf)
        ]
        
        range_names = ["< -2.0", "[-2.0, -1.0)", "[-1.0, 0.0)", "[0.0, 1.0)", "[1.0, 2.0)", ">= 2.0"]
        
        print(f"   不同数值范围的误差分析:")
        print(f"   {'范围':>12} {'像素数':>8} {'平均误差':>12} {'最大误差':>12} {'平均相对误差%':>15}")
        print(f"   {'-'*65}")
        
        for (min_val, max_val), range_name in zip(value_ranges, range_names):
            if min_val == -np.inf:
                mask = original < max_val
            elif max_val == np.inf:
                mask = original >= min_val
            else:
                mask = (original >= min_val) & (original < max_val)
            
            if np.any(mask):
                range_errors = difference[mask]
                range_originals = original[mask]
                
                avg_error = np.mean(range_errors)
                max_error = np.max(range_errors)
                
                # 计算相对误差
                non_zero_in_range = np.abs(range_originals) > 1e-8
                if np.any(non_zero_in_range):
                    avg_rel_error = np.mean(range_errors[non_zero_in_range] / np.abs(range_originals[non_zero_in_range])) * 100
                else:
                    avg_rel_error = 0.0
                
                print(f"   {range_name:>12} {np.sum(mask):>8} {avg_error:>12.6f} {max_error:>12.6f} {avg_rel_error:>14.2f}%")
        
        # 保存详细分析报告
        with open("frame0_channel0_detailed_analysis.txt", "w") as f:
            f.write("帧0通道0详细误差分析报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"基本信息:\n")
            f.write(f"  数据形状: {original.shape}\n")
            f.write(f"  总像素数: {original.size}\n")
            f.write(f"  原始数据范围: [{original.min():.6f}, {original.max():.6f}]\n")
            f.write(f"  重建数据范围: [{reconstructed.min():.6f}, {reconstructed.max():.6f}]\n")
            f.write(f"  差异范围: [{difference.min():.6f}, {difference.max():.6f}]\n")
            f.write(f"  平均绝对误差: {difference.mean():.6f}\n")
            f.write(f"  误差标准差: {difference.std():.6f}\n\n")
            
            f.write(f"量化参数:\n")
            f.write(f"  量化步长: {quantization_step:.8f}\n")
            f.write(f"  1/2量化步长阈值: {half_step_threshold:.8f}\n\n")
            
            f.write(f"误差分布统计:\n")
            for threshold, name in zip(thresholds, threshold_names):
                count = np.sum(difference <= threshold)
                percentage = count / difference.size * 100
                f.write(f"  误差 ≤ {name}: {count}/{difference.size} ({percentage:.1f}%)\n")
            
            f.write(f"\n最大误差位置详情:\n")
            f.write(f"  位置: {max_error_pos}\n")
            f.write(f"  误差值: {max_error_value:.6f}\n")
            f.write(f"  原始值: {max_error_original:.6f}\n")
            f.write(f"  重建值: {max_error_reconstructed:.6f}\n")
            f.write(f"  相对误差: {(max_error_value/abs(max_error_original)*100):.2f}%\n")
        
        print(f"\n✅ 详细分析报告已保存到: frame0_channel0_detailed_analysis.txt")
        
        return {
            'original': original,
            'reconstructed': reconstructed,
            'difference': difference,
            'max_error': max_error_value,
            'mean_error': difference.mean(),
            'std_error': difference.std(),
            'stats': stats
        }
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = analyze_frame0_channel0_detailed()
    
    if result:
        print(f"\n🎯 关键结论:")
        print(f"   最大误差: {result['max_error']:.6f}")
        print(f"   平均误差: {result['mean_error']:.6f}")
        print(f"   误差标准差: {result['std_error']:.6f}")
    else:
        print(f"\n❌ 分析失败")
